import pandas as pd
import os
import html
import re
from pathlib import Path

def clean_and_validate_email(email):
    """
    Clean HTML entities from email and validate if it's a proper email
    """
    # Decode HTML entities
    email = html.unescape(email)

    # Basic email validation pattern
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

    if re.match(email_pattern, email):
        return email
    else:
        return None

def expand_semicolon_separated_data(df):
    """
    Expand semicolon-separated AUTHOR and EMAIL columns into separate rows
    """
    expanded_rows = []

    for _, row in df.iterrows():
        # Split authors and emails by semicolon (handle different column name variations)
        author_col = 'Author Name' if 'Author Name' in df.columns else ('Author' if 'Author' in df.columns else 'AUTHOR')
        email_col = 'Email' if 'Email' in df.columns else 'EMAIL'

        # Decode HTML entities before splitting
        author_text = html.unescape(str(row[author_col]))
        email_text = html.unescape(str(row[email_col]))

        authors = [author.strip() for author in author_text.split(';') if author.strip()]
        emails_raw = [email.strip() for email in email_text.split(';') if email.strip()]

        # Clean and validate emails
        emails = []
        valid_authors = []

        for i, email in enumerate(emails_raw):
            cleaned_email = clean_and_validate_email(email)
            if cleaned_email and i < len(authors):
                emails.append(cleaned_email)
                valid_authors.append(authors[i])
            elif cleaned_email:
                # Email without corresponding author
                emails.append(cleaned_email)
                valid_authors.append('')

        # Use the valid pairs only
        authors = valid_authors
        emails = emails

        # Handle cases where number of authors and emails don't match
        max_count = max(len(authors), len(emails))

        # Pad shorter list with empty strings
        while len(authors) < max_count:
            authors.append('')
        while len(emails) < max_count:
            emails.append('')

        # Create a row for each author-email pair
        for i in range(max_count):
            new_row = row.copy()
            # Remove commas from author names
            author_name = authors[i] if i < len(authors) else ''
            author_name = author_name.replace(',', '') if author_name else ''
            new_row[author_col] = author_name
            new_row[email_col] = emails[i] if i < len(emails) else ''
            expanded_rows.append(new_row)

    return pd.DataFrame(expanded_rows)

def extract_mdpi_data(input_dir=None):
    """
    Extract AUTHOR, EMAIL, TITLE, KEYWORDS, AFFILIATION columns from MDPI txt files
    and save them as separate CSV files in extract-output folder

    Args:
        input_dir (str, optional): Path to the directory containing MDPI txt files.
                                 If None, user will be prompted to enter the path.
    """

    # Get input directory from user if not provided
    if input_dir is None:
        input_dir = input("Enter the path to the directory containing MDPI txt files: ").strip()

        # Remove quotes if user entered path with quotes
        if input_dir.startswith('"') and input_dir.endswith('"'):
            input_dir = input_dir[1:-1]
        elif input_dir.startswith("'") and input_dir.endswith("'"):
            input_dir = input_dir[1:-1]

    # Validate that the directory exists
    if not os.path.exists(input_dir):
        print(f"Error: Directory '{input_dir}' does not exist.")
        return

    if not os.path.isdir(input_dir):
        print(f"Error: '{input_dir}' is not a directory.")
        return

    # Define input directory and files
    output_dir = os.path.join(input_dir, "extract-output")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Define the columns we want to extract (note: EMAIL has a trailing space)
    columns_to_extract = ['AUTHOR', 'EMAIL ', 'TITLE', 'KEYWORDS', 'AFFILIATION']
    
    # Get all txt files in the input directory
    txt_files = [f for f in os.listdir(input_dir) if f.endswith('.txt')]
    
    print(f"Found {len(txt_files)} txt files to process:")
    for file in txt_files:
        print(f"  - {file}")
    
    # Process each file
    for txt_file in txt_files:
        input_path = os.path.join(input_dir, txt_file)
        
        try:
            # Read the tab-delimited file
            print(f"\nProcessing: {txt_file}")
            df = pd.read_csv(input_path, sep='\t', encoding='utf-8')
            
            # Check if all required columns exist
            missing_columns = [col for col in columns_to_extract if col not in df.columns]
            if missing_columns:
                print(f"Warning: Missing columns in {txt_file}: {missing_columns}")
                # Use only available columns
                available_columns = [col for col in columns_to_extract if col in df.columns]
            else:
                available_columns = columns_to_extract
            
            # Extract the specified columns
            extracted_df = df[available_columns].copy()

            # Clean up column names (remove trailing spaces and apply proper case)
            extracted_df.columns = [col.strip().title() for col in extracted_df.columns]

            # Rename columns for better clarity
            column_renames = {}
            if 'Author' in extracted_df.columns:
                column_renames['Author'] = 'Author Name'
            if 'Title' in extracted_df.columns:
                column_renames['Title'] = 'Article Title'

            if column_renames:
                extracted_df = extracted_df.rename(columns=column_renames)

            # Expand semicolon-separated AUTHOR and EMAIL columns
            print(f"  ✓ Expanding semicolon-separated data...")
            expanded_df = expand_semicolon_separated_data(extracted_df)

            # Create output filename (replace .txt with .csv)
            output_filename = txt_file.replace('.txt', '_extracted_expanded.csv')
            output_path = os.path.join(output_dir, output_filename)

            # Save to CSV with proper quoting and UTF-8-SIG encoding
            expanded_df.to_csv(output_path, index=False, encoding='utf-8-sig', quoting=1)
            
            print(f"  ✓ Original records: {len(extracted_df)}")
            print(f"  ✓ Expanded records: {len(expanded_df)}")
            print(f"  ✓ Saved to: {output_path}")
            print(f"  ✓ Columns: {', '.join(available_columns)}")
            
        except Exception as e:
            print(f"  ✗ Error processing {txt_file}: {str(e)}")
    
    print(f"\n=== Extraction Complete ===")
    print(f"Output files saved in: {os.path.abspath(output_dir)}")

if __name__ == "__main__":
    extract_mdpi_data()
